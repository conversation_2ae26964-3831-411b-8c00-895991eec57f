use crate::tools::ToolTrait;
use crate::types::ToolExecutionResult;
use crate::Result;
use async_trait::async_trait;
use serde_json::{json, Value};

/// 简单的回声工具，用于测试
pub struct EchoTool;

#[async_trait]
impl ToolTrait for EchoTool {
    fn name(&self) -> &str {
        "echo"
    }

    fn description(&self) -> &str {
        "Echo back the input message. Useful for testing tool execution."
    }

    fn parameters_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "message": {
                    "type": "string",
                    "description": "The message to echo back"
                }
            },
            "required": ["message"]
        })
    }

    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult> {
        let message = parameters
            .get("message")
            .and_then(|v| v.as_str())
            .unwrap_or("No message provided");

        Ok(ToolExecutionResult::Success {
            output: json!({
                "echoed_message": message,
                "timestamp": chrono::Utc::now().to_rfc3339()
            }),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[tokio::test]
    async fn test_echo_tool_basic_info() {
        let tool = EchoTool;
        assert_eq!(tool.name(), "echo");
        assert!(!tool.description().is_empty());
        assert!(!tool.parameters_schema().is_null());
    }

    #[tokio::test]
    async fn test_echo_tool_execution() {
        let tool = EchoTool;

        let parameters = json!({
            "message": "Hello, World!"
        });

        let result = tool.execute(parameters).await.unwrap();

        match result {
            ToolExecutionResult::Success { output } => {
                assert_eq!(output["echoed_message"], "Hello, World!");
                assert!(output["timestamp"].is_string());
            }
            ToolExecutionResult::Error { .. } => {
                panic!("Echo tool should not fail");
            }
        }
    }

    #[tokio::test]
    async fn test_echo_tool_no_message() {
        let tool = EchoTool;

        let parameters = json!({});

        let result = tool.execute(parameters).await.unwrap();

        match result {
            ToolExecutionResult::Success { output } => {
                assert_eq!(output["echoed_message"], "No message provided");
            }
            ToolExecutionResult::Error { .. } => {
                panic!("Echo tool should handle missing message gracefully");
            }
        }
    }
}
