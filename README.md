# Minimal Agent

一个基于 LLM 驱动的智能代码助手 CLI 工具。

## 功能特性

- 🤖 **LLM 驱动**: 支持 OpenAI 和 OpenRouter API
- 🔧 **工具调用**: 内置文件操作、代码分析等工具
- 💬 **对话管理**: 持久化对话历史
- 🎯 **代码助手**: 专门的代码编写和分析功能
- 🖥️ **优化 CLI**: 支持中文字符精确编辑、命令历史浏览
- ⚡ **智能配置**: 交互式配置向导，自动缓存到 `~/.minimalagent/`



## 安装和运行

### 环境要求
- Rust 1.70+

### 快速开始
```bash
# 克隆并编译
git clone <repository-url>
cd minimal_agent
cargo build --release

# 首次运行（会启动配置向导）
./start_cli.sh interactive
```

### API 密钥配置
首次运行时会自动启动交互式配置向导，支持：
- **OpenRouter** (推荐): 支持更多模型，价格更优
- **OpenAI**: 官方 API

配置完成后自动保存到 `~/.minimalagent/config.json`

也可以通过环境变量配置：
```bash
export OPENROUTER_API_KEY="your-key"  # 或
export OPENAI_API_KEY="your-key"
```

## 使用

### 启动方式
```bash
# 交互模式（推荐）
./start_cli.sh interactive

# 单次查询
./start_cli.sh single

# 直接使用 cargo
cargo run -- --interactive --coding-mode
```

### 配置管理
```bash
cargo run -- --show-config      # 显示配置信息
cargo run -- --clear-config     # 清除配置缓存
cargo run -- --reconfigure      # 重新配置
```

## 技术栈

- **Rust**: 高性能系统编程语言
- **LLM API**: OpenRouter / OpenAI
- **CLI**: rustyline, dialoguer, console
- **数据库**: SQLite
- **配置**: JSON 格式，缓存到 `~/.minimalagent/`

## 开发

### 项目结构
```
src/
├── agent/          # Agent 核心逻辑
├── cli/           # CLI 界面和交互
├── config/        # 配置管理
├── database/       # 数据库操作
├── llm/           # LLM 客户端
├── tools/         # 工具系统
├── prompts/       # 系统提示词
└── main.rs        # 程序入口
```

### 运行测试
```bash
cargo test
```

单元测试直接写在源文件中，使用 `#[cfg(test)]` 模块。

## 许可证

MIT License
